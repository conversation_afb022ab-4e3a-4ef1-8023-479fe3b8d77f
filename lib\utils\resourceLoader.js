import { loadExternalResource } from './index'

/**
 * CDN资源配置
 */
const CDN_CONFIG = {
  live2d: [
    'https://fastly.jsdelivr.net/gh/stevenjoezhang/live2d-widget@latest/live2d.min.js',
    'https://gcore.jsdelivr.net/gh/stevenjoezhang/live2d-widget@latest/live2d.min.js',
    'https://unpkg.com/live2d-widget@latest/live2d.min.js',
    'https://cdn.jsdelivr.net/gh/stevenjoezhang/live2d-widget@latest/live2d.min.js'
  ],
  aplayer: [
    'https://fastly.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js',
    'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.js',
    'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js'
  ],
  aplayerCSS: [
    'https://fastly.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css',
    'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.css',
    'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css'
  ],
  meting: [
    'https://fastly.jsdelivr.net/npm/meting@2.0.1/dist/Meting.min.js',
    'https://unpkg.com/meting@2.0.1/dist/Meting.min.js',
    'https://cdn.jsdelivr.net/npm/meting@2.0.1/dist/Meting.min.js'
  ],
  prism: [
    'https://fastly.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-okaidia.min.css',
    'https://unpkg.com/prismjs@1.29.0/themes/prism-okaidia.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css',
    'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-okaidia.min.css'
  ],
  twikoo: [
    'https://fastly.jsdelivr.net/npm/twikoo@1.6.17/dist/twikoo.all.min.js',
    'https://unpkg.com/twikoo@1.6.17/dist/twikoo.all.min.js',
    'https://cdn.jsdelivr.net/npm/twikoo@1.6.17/dist/twikoo.all.min.js'
  ]
}

/**
 * 带故障转移的资源加载器
 * @param {string} resourceKey - 资源键名
 * @param {string} type - 资源类型 ('js' | 'css')
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<string>} 成功加载的URL
 */
export async function loadResourceWithFallback(resourceKey, type = 'js', timeout = 8000) {
  const urls = CDN_CONFIG[resourceKey]

  if (!urls || urls.length === 0) {
    throw new Error(`未找到资源配置: ${resourceKey}`)
  }

  let lastError = null
  
  for (let i = 0; i < urls.length; i++) {
    const url = urls[i]
    try {
      console.log(`尝试加载 ${resourceKey} 从 CDN ${i + 1}/${urls.length}: ${url}`)
      await loadExternalResource(url, type, timeout)
      console.log(`成功加载 ${resourceKey} 从: ${url}`)
      return url
    } catch (error) {
      lastError = error
      console.warn(`CDN ${i + 1} 加载失败 (${resourceKey}):`, error.message)
      
      // 如果不是最后一个URL，继续尝试下一个
      if (i < urls.length - 1) {
        continue
      }
    }
  }
  
  // 所有CDN都失败了
  throw new Error(`所有CDN都无法加载 ${resourceKey}: ${lastError?.message}`)
}

/**
 * 批量加载资源
 * @param {Array} resources - 资源配置数组 [{key, type}, ...]
 * @returns {Promise<Array>} 加载结果数组
 */
export async function loadMultipleResources(resources) {
  const results = []
  
  for (const resource of resources) {
    try {
      const url = await loadResourceWithFallback(resource.key, resource.type)
      results.push({ success: true, key: resource.key, url })
    } catch (error) {
      results.push({ success: false, key: resource.key, error: error.message })
    }
  }
  
  return results
}

/**
 * 预加载关键资源
 */
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return
  
  // 预加载Live2D
  loadResourceWithFallback('live2d', 'js', 15000).catch(error => {
    console.warn('Live2D预加载失败:', error.message)
  })
  
  // 预加载APlayer
  Promise.all([
    loadResourceWithFallback('aplayer', 'js', 15000),
    loadResourceWithFallback('aplayerCSS', 'css', 15000)
  ]).catch(error => {
    console.warn('APlayer预加载失败:', error.message)
  })
}

/**
 * 检查资源是否已加载
 * @param {string} url - 资源URL
 * @param {string} type - 资源类型
 * @returns {boolean} 是否已加载
 */
export function isResourceLoaded(url, type = 'js') {
  if (typeof document === 'undefined') return false
  
  const selector = type === 'js' ? `script[src="${url}"]` : `link[href="${url}"]`
  return document.querySelector(selector) !== null
}

/**
 * 移除已加载的资源
 * @param {string} url - 资源URL
 * @param {string} type - 资源类型
 */
export function removeResource(url, type = 'js') {
  if (typeof document === 'undefined') return
  
  const selector = type === 'js' ? `script[src="${url}"]` : `link[href="${url}"]`
  const element = document.querySelector(selector)
  
  if (element) {
    element.remove()
  }
}
