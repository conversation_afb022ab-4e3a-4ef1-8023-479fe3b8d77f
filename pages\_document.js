/* eslint-disable @next/next/no-sync-scripts */
import BLOG from '@/blog.config'
import Document, { Head, Html, Main, NextScript } from 'next/document'

class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const initialProps = await Document.getInitialProps(ctx)
    return { ...initialProps }
  }

  render() {
    return (
      <Html lang={BLOG.LANG}>
        <Head>
          {/* 预加载字体 (你原来就有的) */}
          {BLOG.FONT_AWESOME && (
            <>
              <link
                rel='preload'
                href={BLOG.FONT_AWESOME}
                as='style'
                crossOrigin='anonymous'
              />
              <link
                rel='stylesheet'
                href={BLOG.FONT_AWESOME}
                crossOrigin='anonymous'
                referrerPolicy='no-referrer'
              />
            </>
          )}

          {/* ================== 资源预加载优化 START ================== */}

          {/* 1. 提前和关键服务器建立连接 */}
          <link rel='preconnect' href='https://live2d.wobshare.us.kg' />
          <link rel='preconnect' href='https://cdn.jsdelivr.net' />
          <link rel='preconnect' href='https://unpkg.com' />
          <link rel='preconnect' href='https://fastly.jsdelivr.net' />

          {/* 2. 预加载关键资源 */}
          <link
            rel='preload'
            href='https://cdn.jsdelivr.net/gh/stevenjoezhang/live2d-widget@latest/live2d.min.js'
            as='script'
            crossOrigin='anonymous'
          />

          {/* 3. 预加载音乐播放器资源 */}
          <link
            rel='preload'
            href='https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js'
            as='script'
            crossOrigin='anonymous'
          />
          <link
            rel='preload'
            href='https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css'
            as='style'
            crossOrigin='anonymous'
          />

          {/* ================== 资源预加载优化 END ==================== */}
        </Head>

        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}

export default MyDocument
