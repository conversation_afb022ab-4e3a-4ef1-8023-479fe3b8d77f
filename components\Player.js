import { siteConfig } from '@/lib/config'
import { loadResourceWithFallback } from '@/lib/utils/resourceLoader'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')
  const musicMetingCDNUrl = siteConfig(
    'MUSIC_PLAYER_METING_CDN_URL',
    'https://cdnjs.cloudflare.com/ajax/libs/meting/2.0.1/Meting.min.js'
  )

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable) {
      return
    }

    try {
      // 使用新的资源加载器
      await loadResourceWithFallback('aplayer', 'js', 15000)

      if (musicMetingEnable) {
        try {
          await loadResourceWithFallback('meting', 'js', 15000)
        } catch (error) {
          console.warn('MetingJS 加载失败:', error.message)
        }
      }

      // 确保容器存在且APlayer已加载
      if (!meting && window.APlayer && ref.current) {
        // 添加延迟确保DOM完全准备好
        setTimeout(() => {
          try {
            setPlayer(
              new window.APlayer({
                container: ref.current,
                fixed: true,
                lrcType: lrcType,
                autoplay: autoPlay,
                order: order,
                audio: audio,
                // 添加错误处理
                errorHandler: (error) => {
                  console.warn('APlayer 播放错误:', error)
                }
              })
            )
          } catch (error) {
            console.error('APlayer 初始化失败:', error)
          }
        }, 100)
      }
    } catch (error) {
      console.error('音乐播放器初始化失败:', error)
    }
  }

  useEffect(() => {
    initMusicPlayer()
    return () => {
      setPlayer(undefined)
    }
  }, [])

  // 加载CSS样式
  useEffect(() => {
    if (musicPlayerEnable) {
      loadResourceWithFallback('aplayerCSS', 'css', 15000).catch(error => {
        console.warn('APlayer CSS加载失败:', error.message)
      })
    }
  }, [musicPlayerEnable])

  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
